<template>
    <view class="page-container">
        <cu-custom bgColor="none" :isSearch="false" :isBack="design.elect_sheathe == 0">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #000000; font-weight: 600; font-size: 36rpx">📧 站内信</view>
        </cu-custom>

        <view class="tab-container">
            <ui-tab @change="handleChange" :tab="tab_list" align="justify-center" mark="text-blue" tpl="long" />
        </view>

        <view class="content-container">
            <view style="clear: both; height: 0"></view>
            <view v-if="current == '0'" class="operation-bar">
                <view class="operation-title">🛠️ 操作</view>
                <view v-if="!del" @tap="show_add" class="operation-btn select-btn">
                    <text>✅ 选择</text>
                </view>
                <view v-if="del" @tap="show_add" class="operation-btn cancel-btn">
                    <text>❌ 取消</text>
                </view>
            </view>
            <form @submit="del_do">
                <view v-if="current == '0' && del" class="select-all-bar">
                    <view class="select-all-option">
                        <text class="select-all-text">🔄 全选</text>
                        <checkbox @tap="quanxuan" name="cck" class="custom-checkbox round blue"></checkbox>
                    </view>
                    <view v-if="del">
                        <button formType="submit" class="delete-btn">🗑️ 删除</button>
                    </view>
                </view>
                <view class="divider"></view>
                <view class="message-list">
                    <checkbox-group name="is_check">
                        <view
                            class="message-item"
                            v-if="current == '0'"
                            v-for="(item, index) in list"
                            :key="index"
                        >
                            <view class="message-content" @tap="mod_show" :data-key="index">
                                <view class="message-header" :class="{ 'with-checkbox': check_box }">
                                    <text class="message-type">{{ item.skip_type == 3 ? '💬 帖子被评论通知' : '📢 系统通知' }}</text>
                                    <view class="status-tag unread" v-if="item.status == 0 && !check_box">📩 未读</view>
                                    <view class="status-tag read" v-if="item.status == 1 && !check_box">👀 已读</view>
                                </view>
                                <view class="message-body">
                                    {{ item.maring }}
                                </view>
                                <view class="message-footer">
                                    <view
                                        v-if="item.skip_type != 0"
                                        @tap.stop.prevent="url_nav"
                                        :data-id="item.id"
                                        :data-skip_type="item.skip_type"
                                        :data-paper_id="item.paper_id"
                                        class="detail-btn"
                                    >
                                        <text>👀 查看详情</text>
                                        <text class="cuIcon-right"></text>
                                    </view>
                                    <view class="message-time">
                                        <text>🕐 {{ item.clue_time }}</text>
                                    </view>
                                </view>
                            </view>

                            <view v-if="check_box" class="checkbox-container">
                                <checkbox class="message-checkbox round blue" :value="item.id" :checked="item.is == 0 ? false : true"></checkbox>
                            </view>
                        </view>
                    </checkbox-group>
                </view>
            </form>
            <view :class="'custom-modal ' + (modalShow ? 'show' : '')" @touchmove.stop.prevent="true" @tap="hideModal">
                <view class="modal-content">
                    <view class="modal-header">
                        <view class="modal-title">📢 系统通知</view>
                        <view class="modal-close">
                            <text class="cuIcon-close"></text>
                        </view>
                    </view>
                    <view class="modal-body">
                        <scroll-view scroll-y class="modal-scroll">
                            <text class="modal-text">{{ info.maring }}</text>
                        </scroll-view>
                    </view>
                </view>
            </view>

            <view v-if="current == '1'" class="chat-list-container">
                <view class="chat-list">
                    <view
                        :class="'chat-item ' + (modalName_er == 'move-box-er-' + ll_index ? 'move-cur' : '') + (item.deleting ? ' deleting' : '')"
                        @touchstart="ListTouchStart_er"
                        @touchmove="ListTouchMove_er"
                        @touchend="ListTouchEnd_er"
                        :data-target="'move-box-er-' + ll_index"
                        v-for="(item, ll_index) in l_list"
                        :key="ll_index"
                    >
                        <view class="chat-main-content" @tap="url_url" :data-id="item.recent_user_id">
                            <view class="chat-avatar-wrapper">
                                <view class="chat-avatar" :style="'background-image:url(' + item.re_user_head + ');'">
                                    <view v-if="item.msg_count > 0" class="message-count">
                                        {{ item.msg_count }}
                                    </view>
                                </view>
                                <view class="online-indicator"></view>
                            </view>

                            <view class="chat-content">
                                <view class="chat-header">
                                    <view class="chat-username">💬 {{ item.user_nick_name }}</view>
                                    <view class="chat-time">{{ item.blatter_time }}</view>
                                </view>
                                <view class="chat-message">
                                    <text class="message-preview">{{ item.last_msg }}</text>
                                </view>
                            </view>
                        </view>

                        <view class="chat-actions">
                            <view class="close-btn">❌ 关闭</view>
                            <view @tap="del_my_contacts" :data-id="item.id" :data-index="ll_index" class="delete-chat-btn">🗑️ 删除</view>
                        </view>
                    </view>
                </view>
            </view>
            <view :class="'loading-indicator ' + (!my_list_di ? 'loading' : 'over')"></view>
        </view>

        <tabbar id="tabbar" v-if="design.elect_sheathe != 0 || design.shop_arbor == 0" :tabbar="tabbar"></tabbar>
    </view>
</template>

<script>
import tabbar from '@/yl_welore/util/tabbarComponent/tabbar';
import uiTab from '@/yl_welore/colorui/ui-tab/ui-tab';
const app = getApp();
var http = require('../../util/http.js');
export default {
    components: {
        tabbar,
        uiTab
    },
    /**
     * 页面的初始数据
     */
    data() {
        return {
            tab_list: [
                {
                    name: '系统通知 📢',
                },
                {
                    name: '用户留言 💬',
                }
            ],
            http_root: app.globalData.http_root,
            modalShow: false,
            current: '0',
            user_phone: '',
            list: [],
            info: '',
            l_list: [],
            del_mod: false,
            bj_mod: false,
            page: 1,
            l_page: 1,
            my_list_di: false,
            user_male: 0,
            actions: [
                {
                    name: '删除',
                    color: '#fff',
                    fontsize: '20',
                    width: 100,
                    background: '#ed3f14'
                },
                {
                    name: '取消',
                    width: 100,
                    color: '#80848f',
                    fontsize: '20'
                }
            ],
            sum: 0,
            del: false,
            check_box: false,
            quan: false,
            tabbar:{},
            design: {},
            modalName_er: null,
            modalName: null,
            ListTouchStart: 0,
            ListTouchDirection: null,
            del_id: ''
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad (options) {
        this.page = 1;
        this.list = [];
        var e = app.globalData.getCache('userinfo');
        if (!e) {
            console.log(1);
            uni.login({
                success: (res) => {
                    var params = new Object();
                    params.code = res.code;
                    http.POST(app.globalData.api_root + 'Login/index', {
                        params: params,
                        success: (open)=> {
                            // console.log(open);
                            var data = new Object();
                            data.openid = open.data.info.openid;
                            data.session_key = open.data.info.session_key;
                            http.POST(app.globalData.api_root + 'Login/add_tourist', {
                                params: data,
                                success: (d)=> {
                                    //console.log('1111111');
                                    //console.log(d);
                                    app.globalData.setCache('userinfo', d.data.info);
                                    this.get_my_rec();
                                }
                            });
                        }
                    });
                }
            });
        } else {
            this.user_phone = e.user_phone;
            this.get_my_rec();
        }
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow () {
        //wx.hideTabBar();
        var dd = uni.getStorageSync('is_diy');
        console.log(dd);
        if (dd) {
            this.design = dd;
            app.globalData.editTabbar();
        } else {
            this.get_diy();
        }
        if (this.current == '1') {
            this.l_page = 1;
            this.l_list = [];
            this.get_my_private();
        }
        // this.selectComponent('#tabbar').get_user();
    },
    /**
     * 下拉刷新
     */
    onPullDownRefresh () {
        //模拟加载
        setTimeout(() =>{
            uni.hideNavigationBarLoading(); //完成停止加载
            uni.stopPullDownRefresh(); //停止下拉刷新
        }, 1500);
        this.page = 1;
        this.l_page = 1;
        this.list = [];
        this.l_list = [];
        if (this.current == '0') {
            this.get_my_rec();
        }
        if (this.current == '1') {
            this.get_my_private();
        }
    },
    /**
     * 加载下一页
     */
    onReachBottom () {
        if (this.current == '0') {
            this.page = this.page + 1;
            this.get_my_rec();
        } else {
            this.l_page = this.l_page + 1;
            this.get_my_private();
        }
    },
    methods: {
        mod_show(d) {
            var index = d.currentTarget.dataset.key;
            this.modalShow = true;
            this.info = this.list[index];
            var b = app.globalData.api_root + 'Conversation/overall_set';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.list[index].id;
            http.POST(b, {
                params: params,
                success: (res)=> {
                    console.log(res);
                    this.page = 1;
                    this.list = [];
                    this.get_my_rec();
                },
                fail:  ()=> {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success:  (res)=> {}
                    });
                }
            });
        },
        show_add() {
            this.del = !this.del;
            this.check_box = !this.check_box;
        },
        quanxuan(k) {
            this.quan = !this.quan;
            var list = this.list;
            var arr = [];
            for (let i = 0; i < list.length; i++) {
                if (this.quan) {
                    this.$set(this.list[i], 'is', 1);
                } else {
                    this.$set(this.list[i], 'is', 0);
                }
            }
        },
        get_select (c) {
            console.log(c);
            var key = c.detail.index;
            var id = c.currentTarget.dataset.id;
            var index = c.currentTarget.dataset.index;
            if (key == 0) {
                this.del_my_contacts(id, index);
            }
        },
        /**
         * 删除最近联系人
         */
        del_my_contacts (dd) {
            var id = dd.currentTarget.dataset.id;
            var index = dd.currentTarget.dataset.index;

            // 添加触觉反馈
            uni.vibrateShort({
                type: 'medium'
            });

            // 为要删除的项添加动画类标记
            this.$set(this.l_list[index], 'deleting', true);

            // 等待动画完成后再删除数据
            setTimeout(() => {
                var b = app.globalData.api_root + 'User/del_my_contacts';
                var e = app.globalData.getCache('userinfo');
                var params = new Object();
                params.token = e.token;
                params.openid = e.openid;
                params.id = id;
                http.POST(b, {
                    params: params,
                    success: (res)=> {
                        console.log(res);
                        var lists = this.l_list;
                        lists.splice(index, 1);
                        this.l_list = lists;
                        this.modalName_er = null;
                    },
                    fail: ()=> {
                        // 如果删除失败，移除动画类
                        if (this.l_list[index]) {
                            this.$set(this.l_list[index], 'deleting', false);
                        }
                        uni.showModal({
                            title: '提示',
                            content: '网络繁忙，请稍候重试！',
                            showCancel: false,
                            success: ()=> {}
                        });
                    }
                });
            }, 400); // 动画持续时间
        },
        /**
         * 跳转
         */
        url_url (c) {
            var e = app.globalData.getCache('userinfo');
            if (!this.user_phone) {
                uni.showModal({
                    title: '提示',
                    content: '您尚未绑定手机号！',
                    confirmText: '去绑定',
                    success: (res) => {
                        if (res.confirm) {
                            uni.navigateTo({
                                url: '/yl_welore/pages/packageC/service_centre/index'
                            });
                        }
                    }
                });
            } else {
                var id = c.currentTarget.dataset.id;
                uni.navigateTo({
                    url: '/yl_welore/pages/packageB/private_letter/index?id=' + id
                });
            }
        },
        handleChange (detail) {
            var key = detail.detail.index;
            this.current = key;
            this.modalName_er = null;
            this.modalName = null;
            if (key == '0') {
                this.page = 1;
                this.list = [];
                this.my_list_di = false;
                this.get_my_rec();
            } else {
                this.l_page = 1;
                this.l_list = [];
                this.my_list_di = false;
                this.get_my_private();
            }
        },
        get_diy () {
            var b = app.globalData.api_root + 'User/get_diy';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            http.POST(b, {
                params: params,
                success:  (res)=> {
                    console.log(res);
                    this.design = res.data;
                    app.globalData.editTabbar();
                },
                fail:  ()=> {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success:  (res)=> {}
                    });
                }
            });
        },
        longPress (e) {
            console.log(e);
            this.del_id = e.currentTarget.dataset.id;
            this.del_mod = true;
        },
        /**
         *
         */
        url_nav (d) {
            var paper_id = d.currentTarget.dataset.paper_id;
            var skip_type = d.currentTarget.dataset.skip_type;
            var id = d.currentTarget.dataset.id;
            this.up_user_smail(id);
            if (skip_type == 1) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageC/confession/index'
                });
                return;
            }
            if (skip_type == 2) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageA/user_order/index'
                });
                return;
            }
            if (skip_type == 3 && paper_id != 0) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageA/article/index?id=' + paper_id
                });
                return;
            }
            if (skip_type == 4) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageC/delete_posts/index'
                });
                return;
            }
            if (skip_type == 5) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageC/user_report/index'
                });
                return;
            }
        },
        del_user_smail (e) {
            this.del_id = e.currentTarget.dataset.id;
            this.del_mod = true;
        },
        /**
         *
         */
        get_all_mod () {
            this.bj_mod = true;
        },
        hideModal () {
            this.del_mod = false;
            this.bj_mod = false;
            this.modalShow = false;
        },
        /**
         * 全部标为已读
         */
        get_all () {
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            var b = app.globalData.api_root + 'User/up_user_smail_all';
            http.POST(b, {
                params: params,
                success:  (res)=> {},
                fail:  ()=> {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success:  (res)=> {}
                    });
                }
            });
        },
        /**
         * 未读
         */
        up_user_smail (dd) {
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = dd;
            var b = app.globalData.api_root + 'User/up_user_smail';
            http.POST(b, {
                params: params,
                success: (res)=> {
                    //console.log(res);
                    //that.get_my_rec();
                },
                fail: ()=> {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res)=> {}
                    });
                }
            });
        },
        /**
         * 删除站内信
         */
        del_do (dd) {
            console.log(dd);
            var id = dd.detail.value.is_check;
            if (id.length == 0) {
                uni.showToast({
                    title: '请选择一个通知',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = id.toString();
            var b = app.globalData.api_root + 'User/del_user_smail';
            http.POST(b, {
                params: params,
                success: (res)=> {
                    console.log(res);
                    if (res.data.status == 'success') {
                        this.del_mod = false;
                        this.page = 1;
                        this.list = [];
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        this.get_my_rec();
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail:  ()=> {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res)=> {}
                    });
                }
            });
        },
        /**
         * 消息
         */
        get_my_private () {
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.page = this.l_page;
            var b = app.globalData.api_root + 'User/get_my_private';
            var allMsg = this.l_list;
            http.POST(b, {
                params: params,
                success:  (res)=> {
                    console.log(res);
                    if (res.data.status == 'success') {
                        for (var i = 0; i < res.data.info.length; i++) {
                            allMsg.push(res.data.info[i]);
                        }
                        this.l_list = allMsg;
                        this.sum = res.data.sum;
                        if (res.data.info.length == 0 || allMsg.length < 10) {
                            this.my_list_di = true;
                        }
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: ()=> {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res)=> {}
                    });
                }
            });
        },
        get_my_rec () {
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.page = this.page;
            var b = app.globalData.api_root + 'User/get_user_smail';
            var allMsg = this.list;
            http.POST(b, {
                params: params,
                success:  (res)=> {
                    console.log(res);
                    if (res.data.status == 'success') {
                        for (var i = 0; i < res.data.info.length; i++) {
                            allMsg.push(res.data.info[i]);
                        }
                        if (res.data.user_male > 0) {
                            this.$set(this.tab_list[0], 'tag', res.data.user_male);
                        }
                        if (res.data.user_to_count > 0) {
                            this.$set(this.tab_list[1], 'tag', res.data.user_to_count);
                        }
                        this.list = allMsg;
                        this.user_male = res.data.user_male;
                        if (res.data.info.length == 0 || allMsg.length < 10) {
                            this.my_list_di = true;
                        }
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: ()=> {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res)=> {}
                    });
                }
            });
        },
        // ListTouch触摸开始
        ListTouchStart_er(e) {
            this.ListTouchStart = e.touches[0].pageX;
        },
        // ListTouch计算方向
        ListTouchMove_er(e) {
            this.ListTouchDirection = e.touches[0].pageX - this.ListTouchStart > 0 ? 'right' : 'left';
        },
        // ListTouch计算滚动
        ListTouchEnd_er(e) {
            if (this.ListTouchDirection == 'left') {
                this.modalName_er = e.currentTarget.dataset.target;
                // 添加触觉反馈
                uni.vibrateShort({
                    type: 'light'
                });
            } else {
                this.modalName_er = null;
            }
            this.ListTouchDirection = null;
        }
    }
};
</script>
<style>
page {
    background-color: #f8f9fc;
}

.page-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background: linear-gradient(180deg, #e6f3ff 0%, #f8f9fc 100%);
}

.bg-gradient-blue {
    background: linear-gradient(45deg, #4a89dc, #5ca9fb);
}

.tab-container {
    text-align: center;
    background-color: #ffffff;
    border-radius: 12rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    padding: 10rpx;
}

.content-container {
    background-color: #ffffff;
    border-radius: 20rpx 20rpx 0 0;
    padding-bottom: 100px;
    margin-top: 10rpx;
    box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
    flex: 1;
}

.operation-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20rpx 40rpx;
    padding: 20rpx;
    background-color: #f8f9fc;
    border-radius: 12rpx;
}

.operation-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
}

.operation-btn {
    padding: 10rpx 20rpx;
    border-radius: 10rpx;
    font-size: 26rpx;
}

.select-btn {
    color: #4a89dc;
}

.cancel-btn {
    color: #8799a3;
}

.select-all-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20rpx 40rpx;
    padding: 20rpx;
    background-color: #f8f9fc;
    border-radius: 12rpx;
}

.select-all-option {
    display: flex;
    align-items: center;
}

.select-all-text {
    font-size: 32rpx;
    margin-right: 20rpx;
    vertical-align: middle;
}

.custom-checkbox {
    transform: scale(0.8);
    vertical-align: middle;
}

.delete-btn {
    background-color: #ff6b6b;
    color: #ffffff;
    padding: 10rpx 30rpx;
    border-radius: 10rpx;
    font-size: 28rpx;
    line-height: normal;
    display: inline-block;
}

.divider {
    height: 2rpx;
    width: 90%;
    margin: 10rpx auto;
    background-color: #f0f0f0;
}

.message-list {
    padding: 0 20rpx;
}

.message-item {
    position: relative;
    padding: 30rpx;
    margin-bottom: 40rpx;
    border-radius: 12rpx;
    background-color: #ffffff;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    border-left: 8rpx solid #4a89dc;
}

.message-content {
    width: 100%;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
}

.message-header.with-checkbox {
    padding-right: 80rpx;
}

.message-type {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
}

.status-tag {
    padding: 6rpx 16rpx;
    border-radius: 30rpx;
    font-size: 24rpx;
}

.unread {
    background-color: #ff6b6b;
    color: #ffffff;
}

.read {
    background-color: #52c41a;
    color: #ffffff;
}

.message-body {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
    margin: 20rpx 0;
    word-break: break-all;
}

.message-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20rpx;
    padding-top: 20rpx;
    border-top: 1rpx solid #f0f0f0;
}

.detail-btn {
    color: #4a89dc;
    font-size: 26rpx;
    display: flex;
    align-items: center;
}

.message-time {
    font-size: 24rpx;
    color: #999;
}

.checkbox-container {
    position: absolute;
    right: 20rpx;
    top: 20rpx;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    padding: 10rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.message-checkbox {
    transform: scale(0.9);
}

.custom-modal {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1110;
    opacity: 0;
    outline: 0;
    text-align: center;
    transform: scale(1.185);
    backface-visibility: hidden;
    perspective: 2000rpx;
    background: rgba(0, 0, 0, 0.6);
    transition: all 0.3s ease-in-out 0s;
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-modal.show {
    opacity: 1;
    transform: scale(1);
    overflow-x: hidden;
    overflow-y: auto;
    pointer-events: auto;
}

.modal-content {
    width: 80%;
    max-width: 600rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    background-color: #4a89dc;
    color: #ffffff;
}

.modal-title {
    font-size: 32rpx;
    font-weight: 600;
}

.modal-close {
    font-size: 36rpx;
}

.modal-body {
    padding: 40rpx;
}

.modal-scroll {
    height: 800rpx;
    text-align: left;
}

.modal-text {
    font-size: 28rpx;
    line-height: 1.6;
    color: #333;
}

.chat-list-container {
    margin-top: 20rpx;
    padding: 0 20rpx;
}

.chat-list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.chat-item {
    position: relative;
    border-radius: 16rpx;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%);
    box-shadow: 0 4rpx 20rpx rgba(74, 137, 220, 0.1);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 2rpx solid transparent;
    transform: translateX(0);
}

.chat-item:hover {
    transform: translateY(-2rpx) translateX(0);
    box-shadow: 0 8rpx 30rpx rgba(74, 137, 220, 0.15);
    border-color: #4a89dc;
}

.chat-item.move-cur {
    transform: translateX(0rpx);
    box-shadow: 0 8rpx 30rpx rgba(255, 107, 107, 0.2);
}

.chat-item.move-cur .chat-main-content {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 252, 0.95) 100%);
}

.chat-main-content {
    display: flex;
    align-items: center;
    padding: 30rpx;
    width: 100%;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-radius: 16rpx;
}

.chat-avatar-wrapper {
    position: relative;
    margin-right: 24rpx;
}

.chat-avatar {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    position: relative;
    border: 4rpx solid #ffffff;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.online-indicator {
    position: absolute;
    bottom: 8rpx;
    right: 8rpx;
    width: 20rpx;
    height: 20rpx;
    background-color: #52c41a;
    border-radius: 50%;
    border: 3rpx solid #ffffff;
}

.message-count {
    position: absolute;
    top: -8rpx;
    right: -8rpx;
    background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
    color: #ffffff;
    border-radius: 50%;
    font-size: 22rpx;
    font-weight: 600;
    min-width: 36rpx;
    height: 36rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3rpx solid #ffffff;
    box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}

.chat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8rpx;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-username {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8rpx;
}

.chat-time {
    font-size: 24rpx;
    color: #999;
    background-color: #f0f0f0;
    padding: 4rpx 12rpx;
    border-radius: 12rpx;
}

.chat-message {
    margin-top: 8rpx;
}

.message-preview {
    font-size: 28rpx;
    color: #666;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.chat-actions {
    display: flex;
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 360rpx;
    transform: translateX(100%);
    border-radius: 0 16rpx 16rpx 0;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    opacity: 0;
}

.close-btn, .delete-chat-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 40rpx;
    color: #ffffff;
    height: 100%;
    font-size: 28rpx;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: translateX(20rpx);
    opacity: 0;
}

.close-btn {
    background: linear-gradient(45deg, #8799a3, #a0adb8);
    min-width: 120rpx;
}

.close-btn:active {
    background: linear-gradient(45deg, #6b7780, #8799a3);
}

.delete-chat-btn {
    background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
    min-width: 140rpx;
}

.delete-chat-btn:active {
    background: linear-gradient(45deg, #ff4757, #ff6b6b);
}

.chat-item.move-cur .chat-actions {
    transform: translateX(0);
    opacity: 1;
}

.chat-item.move-cur .close-btn,
.chat-item.move-cur .delete-chat-btn {
    transform: translateX(0);
    opacity: 1;
}

.chat-item.move-cur .close-btn {
    transition-delay: 0.05s;
}

.chat-item.move-cur .delete-chat-btn {
    transition-delay: 0.1s;
}

/* 删除动画 */
.chat-item.deleting {
    animation: slideOutAndFade 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes slideOutAndFade {
    0% {
        transform: translateX(-260rpx);
        opacity: 1;
        height: auto;
        margin-bottom: 20rpx;
    }
    50% {
        transform: translateX(-400rpx);
        opacity: 0.3;
    }
    100% {
        transform: translateX(-600rpx);
        opacity: 0;
        height: 0;
        margin-bottom: 0;
        padding-top: 0;
        padding-bottom: 0;
    }
}

/* 滑动时的微妙效果 */
.chat-item.move-cur .chat-avatar {
    transform: scale(0.95);
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.chat-item.move-cur .message-count {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

.loading-indicator {
    display: block;
    line-height: 3em;
    text-align: center;
    color: #8799a3;
}

.loading-indicator.loading::before {
    content: '加载中...';
}

.loading-indicator.over::before {
    content: '没有更多了';
}

button::after {
    line-height: normal;
    font-size: 30rpx;
    width: 0;
    height: 0;
    top: 0;
    left: 0;
}

button {
    line-height: normal;
    display: block;
    padding-left: 0px;
    padding-right: 0px;
    background-color: rgba(255, 255, 255, 0);
    font-size: 30rpx;
}
</style>
